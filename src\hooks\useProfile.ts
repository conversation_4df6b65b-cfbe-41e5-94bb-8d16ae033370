import { useState, useEffect } from 'react';
import { profileApi, Profile, ProfileUpdateData } from '../lib/api';

interface UseProfileReturn {
  profile: Profile | null;
  isLoading: boolean;
  error: string | null;
  updateProfile: (profileData: ProfileUpdateData) => Promise<boolean>;
  refreshProfile: () => Promise<void>;
  isUpdating: boolean;
}

export function useProfile(): UseProfileReturn {
  const [profile, setProfile] = useState<Profile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const refreshProfile = async () => {
    try {
      setError(null);
      const response = await profileApi.getProfile();
      
      if (response.success) {
        setProfile(response.data);
      } else {
        setError(response.message || 'Failed to fetch profile');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const updateProfile = async (profileData: ProfileUpdateData): Promise<boolean> => {
    try {
      setIsUpdating(true);
      setError(null);

      const response = await profileApi.updateProfile(profileData);
      
      if (response.success) {
        setProfile(response.data);
        return true;
      } else {
        setError(response.message || 'Failed to update profile');
        return false;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
      return false;
    } finally {
      setIsUpdating(false);
    }
  };

  useEffect(() => {
    refreshProfile();
  }, []);

  return {
    profile,
    isLoading,
    error,
    updateProfile,
    refreshProfile,
    isUpdating,
  };
}
