"use client"

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>readcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Link, Path } from "react-router-dom"

// Define route labels for better display names
const routeLabels: Record<string, string> = {
  "dashboard": "Dashboard",
}

// Function to convert route segment to display name
function getRouteLabel(segment: string): string {
  return routeLabels[segment] || segment.charAt(0).toUpperCase() + segment.slice(1)
}

export function AutoBreadcrumb() {
  const pathname = window.location.pathname
  
  // Split pathname into segments and filter out empty strings
  const segments = pathname.split("/").filter(Boolean)
  
  // If we're on the home page, show just "Home"
  if (segments.length === 0) {
    return (
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbPage>Home</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
    )
  }

  return (
    <Breadcrumb>
      <BreadcrumbList>
        {/* Home link */}
        {/* Generate breadcrumb items for each segment */}
        {segments.map((segment, index) => {
          const href = "/" + segments.slice(0, index + 1).join("/")
          console.log({segment, href})
          const isLast = index === segments.length - 1
          const label = getRouteLabel(segment)

          return (
            <div key={href} className="flex items-center">
                {index > 0 && <BreadcrumbSeparator className="hidden md:block" />}
              <BreadcrumbItem className="hidden md:block">
                {isLast ? (
                  <BreadcrumbPage>{label}</BreadcrumbPage>
                ) : (
                  <BreadcrumbLink asChild>
                    <Link to={href}>{label}</Link>
                  </BreadcrumbLink>
                )}
              </BreadcrumbItem>
            </div>
          )
        })}
      </BreadcrumbList>
    </Breadcrumb>
  )
}
