import { useState } from "react";
import { AppSidebar } from "@/components/sidebar/app-sidebar";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";
import { Edit, X } from "lucide-react";
import { useProfile } from "@/hooks/useProfile";
import { ProfileView } from "@/components/profile/ProfileView";
import { ProfileEdit } from "@/components/profile/ProfileEdit";
import { ProfileUpdateData } from "@/lib/api";

export default function ProfilePage() {
  const [isEditing, setIsEditing] = useState(false);
  const { profile, isLoading, error, updateProfile, isUpdating } = useProfile();

  const handleEditToggle = () => {
    setIsEditing(!isEditing);
  };

  const handleSave = async (profileData: ProfileUpdateData) => {
    const success = await updateProfile(profileData);
    if (success) {
      setIsEditing(false);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
  };

  if (!profile && !isLoading) {
    return (
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
            <p className="text-gray-600">No profile data available</p>
          </div>
        </SidebarInset>
      </SidebarProvider>
    );
  }

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 h-4" />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbPage>Profile</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
          <div className="ml-auto px-4">
            {!isEditing ? (
              <Button onClick={handleEditToggle} variant="outline" size="sm">
                <Edit className="h-4 w-4 mr-2" />
                Edit Profile
              </Button>
            ) : (
              <div className="flex gap-2">
                <Button
                  onClick={handleCancel}
                  variant="outline"
                  size="sm"
                  disabled={isUpdating}
                >
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </Button>
              </div>
            )}
          </div>
        </header>
        <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
          {isLoading ? (
            <div className="flex items-center justify-center min-h-[400px]">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center min-h-[400px]">
              <div className="text-center">
                <p className="text-red-600 mb-2">Error loading profile</p>
                <p className="text-sm text-gray-600">{error}</p>
              </div>
            </div>
          ) : profile ? (
            <div className="max-w-4xl mx-auto w-full">
              {isEditing ? (
                <ProfileEdit
                  profile={profile}
                  onSave={handleSave}
                  onCancel={handleCancel}
                  isUpdating={isUpdating}
                />
              ) : (
                <ProfileView profile={profile} />
              )}
            </div>
          ) : (
            <div className="flex items-center justify-center min-h-[400px]">
              <p className="text-gray-600">No profile data available</p>
            </div>
          )}
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}